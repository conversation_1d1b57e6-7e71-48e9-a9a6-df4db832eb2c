import os
import requests
import markdownify
from bs4 import BeautifulSoup
import re
import unicodedata
import time
import json
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional, Dict, Union
from pathlib import Path
from dataclasses import dataclass, field
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import webbrowser
from urllib.parse import urlparse

# 尝试导入 markitdown，如果不存在则提示安装
try:
    from markitdown import MarkItDown
    MARKITDOWN_AVAILABLE = True
except ImportError:
    MARKITDOWN_AVAILABLE = False
    print("警告: markitdown 库未安装。请运行 'pip install markitdown[all]' 来启用多格式文件转换功能。")

# 检查 pytesseract 可用性
try:
    import pytesseract  # type: ignore
    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False

@dataclass
class ContentFilterConfig:
    """内容过滤配置"""
    paragraph_keywords: List[str] = field(default_factory=list)
    image_hashes: List[str] = field(default_factory=list)
    skip_ads: bool = False
    skip_promotions: bool = False

class EnhancedMarkdownConverter:
    """增强的 Markdown 转换器，支持网页和多种文件格式"""
    
    CONFIG_FILE = "enhanced_converter_config.json"
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self.CONFIG_FILE
        self.filter_config = self._load_config()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.processed_titles = set()
        self.lock = threading.Lock()
        
        # 初始化 MarkItDown（如果可用）
        if MARKITDOWN_AVAILABLE:
            self.markitdown = MarkItDown()
        else:
            self.markitdown = None
    
    @staticmethod
    def hash_byte_data(byte_data: bytes) -> str:
        import hashlib
        return hashlib.sha256(byte_data).hexdigest()
    
    @staticmethod
    def remove_nonvisible_chars(text: str) -> str:
        return ''.join(c for c in text if (unicodedata.category(c) != 'Cn'
                                           and c not in (' ', '\n', '\r')))
    
    def _load_config(self) -> ContentFilterConfig:
        default_config = ContentFilterConfig()
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    return ContentFilterConfig(
                        paragraph_keywords=config_data.get('paragraph_keywords', []),
                        image_hashes=config_data.get('image_hashes', []),
                        skip_ads=config_data.get('skip_ads', False),
                        skip_promotions=config_data.get('skip_promotions', False)
                    )
        except Exception as e:
            print(f"加载配置文件失败，将使用默认配置: {str(e)}")
        return default_config
    
    def _save_config(self) -> None:
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'paragraph_keywords': self.filter_config.paragraph_keywords,
                    'image_hashes': self.filter_config.image_hashes,
                    'skip_ads': self.filter_config.skip_ads,
                    'skip_promotions': self.filter_config.skip_promotions
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
    
    def extract_image_info(self, file_path: str) -> str:
        """提取图片的详细信息"""
        try:
            from PIL import Image
            from PIL.ExifTags import TAGS
            import os
            import datetime

            # 获取基本文件信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            file_mtime = datetime.datetime.fromtimestamp(file_stat.st_mtime)

            # 打开图片获取基本信息
            with Image.open(file_path) as img:
                width, height = img.size
                format_name = img.format
                mode = img.mode

                # 尝试提取 EXIF 信息
                exif_info = {}
                if hasattr(img, '_getexif') and img._getexif():
                    exif_data = img._getexif()
                    for tag_id, value in exif_data.items():
                        tag = TAGS.get(tag_id, tag_id)
                        exif_info[tag] = value

                # 构建 Markdown 内容
                markdown_content = f"# 图片文件信息\n\n"
                markdown_content += f"## 基本信息\n\n"
                markdown_content += f"- **文件名**: {Path(file_path).name}\n"
                markdown_content += f"- **文件大小**: {self._format_file_size(file_size)}\n"
                markdown_content += f"- **修改时间**: {file_mtime.strftime('%Y-%m-%d %H:%M:%S')}\n"
                markdown_content += f"- **图片格式**: {format_name}\n"
                markdown_content += f"- **图片尺寸**: {width} × {height} 像素\n"
                markdown_content += f"- **颜色模式**: {mode}\n\n"

                # 添加 EXIF 信息
                if exif_info:
                    markdown_content += f"## EXIF 信息\n\n"
                    for key, value in exif_info.items():
                        if isinstance(value, (str, int, float)):
                            markdown_content += f"- **{key}**: {value}\n"
                    markdown_content += "\n"

                # 尝试 OCR 识别文字
                ocr_text = self._extract_text_with_ocr(file_path)
                if ocr_text:
                    markdown_content += f"## OCR 识别文字\n\n{ocr_text}\n\n"
                else:
                    markdown_content += f"## OCR 识别结果\n\n*未识别到文字内容*\n\n"

                return markdown_content

        except Exception as e:
            print(f"提取图片信息失败 {file_path}: {str(e)}")
            file_name = Path(file_path).name
            return f"# 图片文件\n\n文件名: {file_name}\n\n*注意: 无法提取详细图片信息*\n"

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def _extract_text_with_ocr(self, file_path: str) -> Optional[str]:
        """使用 OCR 提取图片中的文字"""
        # 首先尝试使用 pytesseract
        ocr_result = self._try_pytesseract_ocr(file_path)
        if ocr_result:
            return ocr_result

        # 如果 pytesseract 不可用，尝试使用简单的图像分析
        return self._try_simple_text_detection(file_path)

    def _try_pytesseract_ocr(self, file_path: str) -> Optional[str]:
        """尝试使用 pytesseract 进行 OCR"""
        if not PYTESSERACT_AVAILABLE:
            return None

        try:
            import pytesseract  # type: ignore
            from PIL import Image

            # 打开图片并进行 OCR
            with Image.open(file_path) as img:
                # 转换为 RGB 模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 使用 tesseract 进行 OCR，支持中英文
                text = pytesseract.image_to_string(img, lang='chi_sim+eng')

                # 清理文本
                text = text.strip()
                if text:
                    return text
                else:
                    return None

        except ImportError:
            return None
        except Exception as e:
            print(f"pytesseract OCR 失败: {str(e)}")
            return None

    def _try_simple_text_detection(self, file_path: str) -> Optional[str]:
        """简单的文本检测（基于图像特征）"""
        try:
            from PIL import Image
            import numpy as np

            with Image.open(file_path) as img:
                # 转换为灰度图
                gray_img = img.convert('L')

                # 转换为 numpy 数组
                img_array = np.array(gray_img)

                # 简单的文本区域检测
                # 计算图像的一些统计特征
                mean_brightness = np.mean(img_array)
                std_brightness = np.std(img_array)

                # 检测是否可能包含文字（基于亮度分布）
                if std_brightness > 30:  # 有一定的对比度
                    # 检测边缘密度
                    edges = self._detect_edges(img_array)
                    edge_density = np.sum(edges) / edges.size

                    if edge_density > 0.1:  # 有足够的边缘，可能包含文字
                        return f"*检测到可能的文字区域，但需要 OCR 工具进行识别*\n\n图像特征:\n- 平均亮度: {mean_brightness:.1f}\n- 亮度标准差: {std_brightness:.1f}\n- 边缘密度: {edge_density:.3f}"

                return None

        except ImportError:
            return None
        except Exception as e:
            print(f"简单文本检测失败: {str(e)}")
            return None

    def _detect_edges(self, img_array):
        """简单的边缘检测"""
        try:
            import numpy as np

            # 简单的 Sobel 边缘检测
            sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
            sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])

            # 应用卷积（简化版）
            height, width = img_array.shape
            edges = np.zeros_like(img_array)

            for i in range(1, height - 1):
                for j in range(1, width - 1):
                    gx = np.sum(sobel_x * img_array[i-1:i+2, j-1:j+2])
                    gy = np.sum(sobel_y * img_array[i-1:i+2, j-1:j+2])
                    edges[i, j] = min(255, np.sqrt(gx**2 + gy**2))

            return edges > 50  # 二值化边缘

        except:
            # 如果失败，返回零数组
            return np.zeros_like(img_array, dtype=bool)

    def convert_file_with_markitdown(self, file_path: str) -> Optional[str]:
        """使用 MarkItDown 转换文件"""
        if not MARKITDOWN_AVAILABLE or not self.markitdown:
            return None

        try:
            result = self.markitdown.convert(file_path)
            if hasattr(result, 'text_content'):
                if result.text_content and result.text_content.strip():
                    return result.text_content
                else:
                    # 对于图片等可能没有文本内容的文件，提供更友好的处理
                    file_ext = Path(file_path).suffix.lower()
                    if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                        print(f"图片文件 {file_path} 转换成功，但未提取到文本内容，尝试提取图片信息...")
                        # 使用增强的图片信息提取
                        return self.extract_image_info(file_path)
                    else:
                        print(f"文件 {file_path} 转换成功，但内容为空")
                        return None
            else:
                print(f"MarkItDown 转换结果异常 {file_path}: 无 text_content 属性")
                return None
        except Exception as e:
            print(f"MarkItDown 转换失败 {file_path}: {str(e)}")
            # 如果是图片文件，尝试直接提取图片信息
            file_ext = Path(file_path).suffix.lower()
            if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                print(f"尝试直接提取图片信息...")
                return self.extract_image_info(file_path)
            return None
    
    def convert_url_with_markitdown(self, url: str) -> Optional[str]:
        """使用 MarkItDown 转换 URL"""
        if not MARKITDOWN_AVAILABLE or not self.markitdown:
            return None
        
        try:
            result = self.markitdown.convert(url)
            return result.text_content
        except Exception as e:
            print(f"MarkItDown URL 转换失败 {url}: {str(e)}")
            return None
    
    def is_supported_file_format(self, file_path: str) -> bool:
        """检查文件格式是否被 MarkItDown 支持"""
        if not MARKITDOWN_AVAILABLE:
            return False
        
        supported_extensions = {
            '.pdf', '.docx', '.xlsx', '.pptx', '.html', '.htm', 
            '.txt', '.md', '.csv', '.json', '.xml', '.epub',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
            '.mp3', '.wav', '.m4a', '.flac', '.ogg',
            '.zip', '.msg', '.ipynb'
        }
        
        file_ext = Path(file_path).suffix.lower()
        return file_ext in supported_extensions
    
    def filter_content(self, text: str) -> str:
        if not self.filter_config:
            return text
        if self.filter_config.paragraph_keywords:
            text = self._filter_paragraphs(text, self.filter_config.paragraph_keywords)
        return text
    
    def _filter_paragraphs(self, text: str, keywords: List[str]) -> str:
        lines = [line.strip() for line in text.split('\n')]
        filtered_lines = []
        current_paragraph = []
        
        for line in lines:
            if not line.strip():
                if not self._paragraph_contains_keywords(current_paragraph, keywords):
                    filtered_lines.extend(current_paragraph)
                current_paragraph = []
            else:
                current_paragraph.append(line)
        
        if not self._paragraph_contains_keywords(current_paragraph, keywords):
            filtered_lines.extend(current_paragraph)
        
        return '\n\n'.join(filtered_lines) + '\n\n'
    
    def _paragraph_contains_keywords(self, paragraph: List[str], keywords: List[str]) -> bool:
        paragraph_text = ' '.join(paragraph)
        return any(keyword in paragraph_text for keyword in keywords)
    
    def convert_wechat_article(self, url: str, title: str, create_time: str,
                              content_soup: BeautifulSoup, account_dir: str) -> tuple:
        """转换微信文章为 Markdown"""
        # 移除所有图片标签
        for img in content_soup.find_all('img'):
            img.decompose()
        
        # 转换为 markdown 格式
        markdown_content = markdownify.markdownify(str(content_soup))
        markdown_content = '\n'.join([line + '\n' for line in markdown_content.split('\n') if line.strip()])
        clean_title = self.remove_nonvisible_chars(title)
        
        markdown = f'# {clean_title}\n\n{create_time}\n\n{markdown_content}\n'
        markdown = re.sub('\xa0{1,}', '\n', markdown, flags=re.UNICODE)
        markdown = re.sub(r'\]\(http([^)]*)\)',
                          lambda x: '](http' + x.group(1).replace(' ', '%20') + ')',
                          markdown)
        
        return self.filter_content(markdown), clean_title
    
    def get_title_with_retry(self, url: str, max_retries: int = 3) -> tuple:
        """获取网页标题和内容"""
        retries = 0
        while retries < max_retries:
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.text, 'lxml')
                title_element = soup.find('h1', id="activity-name")
                if not title_element:
                    title_element = soup.find('h2', class_="rich_media_title") or \
                                    soup.find('h1', class_="article-title")
                
                if title_element:
                    title = title_element.text.strip()
                    if title:
                        return title, soup
                
                raise AttributeError("Title element not found")
                
            except Exception as e:
                retries += 1
                error_msg = str(e)
                if retries < max_retries:
                    print(f"重试 ({retries}/{max_retries}) URL {url}: 错误 - {error_msg}")
                    time.sleep(2 ** retries)
                else:
                    print(f"获取标题失败 {url} 经过 {max_retries} 次重试。最后错误: {error_msg}")
                    return None, None
    
    def process_file(self, file_path: str, output_dir: str) -> bool:
        """处理单个文件"""
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 使用 MarkItDown 转换文件
        markdown_content = self.convert_file_with_markitdown(file_path)
        if not markdown_content:
            print(f"无法转换文件: {file_path}")
            return False
        
        # 生成输出文件名
        file_name = Path(file_path).stem
        clean_filename = re.sub(r'[\\/*?:"<>|]', '', file_name)
        output_path = os.path.join(output_dir, f"{clean_filename}.md")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 应用内容过滤
        filtered_content = self.filter_content(markdown_content)
        
        # 保存文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(filtered_content)
            print(f'已转换文件: {clean_filename}.md')
            return True
        except Exception as e:
            print(f"保存文件失败 {output_path}: {str(e)}")
            return False
    
    def process_url(self, url_data: Dict, output_base: str) -> bool:
        """处理单个 URL"""
        url = url_data['url']
        
        # 首先尝试使用 MarkItDown 转换 URL
        if MARKITDOWN_AVAILABLE:
            markdown_content = self.convert_url_with_markitdown(url)
            if markdown_content:
                # 从 URL 生成文件名
                parsed_url = urlparse(url)
                domain = parsed_url.netloc.replace('.', '_')
                path_parts = [p for p in parsed_url.path.split('/') if p]
                if path_parts:
                    filename_base = f"{domain}_{path_parts[-1]}"
                else:
                    filename_base = domain
                
                filename_base = re.sub(r'[\\/*?:"<>|]', '', filename_base)
                output_dir = os.path.join(output_base, "MD文档")
                os.makedirs(output_dir, exist_ok=True)
                filepath = os.path.join(output_dir, f"{filename_base}.md")
                
                # 应用内容过滤
                filtered_content = self.filter_content(markdown_content)
                
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(filtered_content)
                    print(f'已转换 URL: {filename_base}.md')
                    return True
                except Exception as e:
                    print(f"保存文件失败 {filepath}: {str(e)}")
        
        # 如果 MarkItDown 失败，回退到原始的微信文章处理方法
        account_dir = os.path.join(output_base, "MD文档")
        
        title, soup = self.get_title_with_retry(url)
        if not title or not soup:
            return False
        
        clean_title = self.remove_nonvisible_chars(title)
        filename_base = re.sub(r'[\\/*?:"<>|]', '', clean_title)
        filepath = os.path.join(account_dir, f"{filename_base}.md")
        
        with self.lock:
            if filename_base in self.processed_titles or os.path.exists(filepath):
                print(f'标题 "{filename_base}" 已存在，跳过: {url}')
                return False
            self.processed_titles.add(filename_base)
        
        content_soup = soup.find('div', {'class': 'rich_media_content'})
        if not content_soup:
            return False
        
        markdown, clean_title = self.convert_wechat_article(url, title, "", content_soup, account_dir)
        
        with self.lock:
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(markdown)
                print(f'已处理: MD文档 - {filename_base}.md')
                return True
            except Exception as e:
                print(f"保存文件失败 {filepath}: {str(e)}")
                return False

class ConverterGUI:
    """图形用户界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("增强型 Markdown 转换器")
        self.root.geometry("800x600")

        self.converter = EnhancedMarkdownConverter()
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="增强型 Markdown 转换器",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # MarkItDown 状态
        status_text = "MarkItDown 已启用" if MARKITDOWN_AVAILABLE else "MarkItDown 未安装"
        status_color = "green" if MARKITDOWN_AVAILABLE else "red"
        status_label = ttk.Label(main_frame, text=f"状态: {status_text}",
                                foreground=status_color)
        status_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))

        # 输入类型选择
        ttk.Label(main_frame, text="输入类型:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.input_type = tk.StringVar(value="url")
        ttk.Radiobutton(main_frame, text="URL", variable=self.input_type,
                       value="url").grid(row=2, column=1, sticky=tk.W)
        ttk.Radiobutton(main_frame, text="文件", variable=self.input_type,
                       value="file").grid(row=2, column=2, sticky=tk.W)

        # URL/文件输入区域
        ttk.Label(main_frame, text="输入内容:").grid(row=3, column=0, sticky=tk.W, pady=5)

        # URL 输入框
        self.url_frame = ttk.Frame(main_frame)
        self.url_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        self.url_frame.columnconfigure(0, weight=1)

        self.url_text = scrolledtext.ScrolledText(self.url_frame, height=6, width=60)
        self.url_text.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        url_buttons_frame = ttk.Frame(self.url_frame)
        url_buttons_frame.grid(row=0, column=1, sticky=tk.N)

        ttk.Button(url_buttons_frame, text="清空",
                  command=self.clear_urls).grid(row=0, column=0, pady=2)
        ttk.Button(url_buttons_frame, text="从文件加载",
                  command=self.load_urls_from_file).grid(row=1, column=0, pady=2)

        # 文件选择区域
        self.file_frame = ttk.Frame(main_frame)
        self.file_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        self.file_frame.columnconfigure(0, weight=1)

        self.file_listbox = tk.Listbox(self.file_frame, height=6)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        file_buttons_frame = ttk.Frame(self.file_frame)
        file_buttons_frame.grid(row=0, column=1, sticky=tk.N)

        ttk.Button(file_buttons_frame, text="添加文件",
                  command=self.add_files).grid(row=0, column=0, pady=2)
        ttk.Button(file_buttons_frame, text="添加文件夹",
                  command=self.add_folder).grid(row=1, column=0, pady=2)
        ttk.Button(file_buttons_frame, text="清空列表",
                  command=self.clear_files).grid(row=2, column=0, pady=2)
        ttk.Button(file_buttons_frame, text="删除选中",
                  command=self.remove_selected_files).grid(row=3, column=0, pady=2)

        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=6, column=0, sticky=tk.W, pady=(10, 5))

        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(0, weight=1)

        self.output_dir = tk.StringVar(value="./articles")
        ttk.Entry(output_frame, textvariable=self.output_dir).grid(row=0, column=0,
                                                                  sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(output_frame, text="浏览",
                  command=self.browse_output_dir).grid(row=0, column=1)

        # 高级选项
        options_frame = ttk.LabelFrame(main_frame, text="高级选项", padding="5")
        options_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(1, weight=1)

        ttk.Label(options_frame, text="并发线程数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.max_workers = tk.IntVar(value=10)
        ttk.Spinbox(options_frame, from_=1, to=50, textvariable=self.max_workers,
                   width=10).grid(row=0, column=1, sticky=tk.W, padx=5)

        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=9, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="开始转换", command=self.start_conversion).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="打开输出目录",
                  command=self.open_output_dir).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="退出",
                  command=self.root.quit).grid(row=0, column=2, padx=5)

        # 进度条和状态
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        self.status_text = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_text).grid(row=11, column=0,
                                                                 columnspan=3, pady=5)

        # 初始化界面状态
        self.update_ui_state()

        # 绑定事件
        self.input_type.trace('w', self.on_input_type_change)

    def update_ui_state(self):
        """更新界面状态"""
        if self.input_type.get() == "url":
            self.url_frame.grid()
            self.file_frame.grid_remove()
        else:
            self.url_frame.grid_remove()
            self.file_frame.grid()

    def on_input_type_change(self, *args):
        """输入类型改变时的回调"""
        self.update_ui_state()

    def clear_urls(self):
        """清空 URL 输入"""
        self.url_text.delete(1.0, tk.END)

    def load_urls_from_file(self):
        """从文件加载 URL"""
        file_path = filedialog.askopenfilename(
            title="选择包含 URL 的文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.url_text.insert(tk.END, content)
            except Exception as e:
                messagebox.showerror("错误", f"读取文件失败: {str(e)}")

    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(
            title="选择要转换的文件",
            filetypes=[
                ("所有支持的格式", "*.pdf;*.docx;*.xlsx;*.pptx;*.html;*.htm;*.txt;*.md;*.csv;*.json;*.xml;*.epub;*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.mp3;*.wav;*.m4a;*.flac;*.ogg;*.zip;*.msg;*.ipynb"),
                ("PDF 文件", "*.pdf"),
                ("Word 文档", "*.docx"),
                ("Excel 文件", "*.xlsx"),
                ("PowerPoint 文件", "*.pptx"),
                ("网页文件", "*.html;*.htm"),
                ("文本文件", "*.txt;*.md"),
                ("数据文件", "*.csv;*.json;*.xml"),
                ("图片文件", "*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff"),
                ("音频文件", "*.mp3;*.wav;*.m4a;*.flac;*.ogg"),
                ("其他文件", "*.epub;*.zip;*.msg;*.ipynb"),
                ("所有文件", "*.*")
            ]
        )
        for file in files:
            if file not in self.file_listbox.get(0, tk.END):
                self.file_listbox.insert(tk.END, file)

    def add_folder(self):
        """添加文件夹中的所有支持文件"""
        folder = filedialog.askdirectory(title="选择包含文件的文件夹")
        if folder:
            supported_extensions = {
                '.pdf', '.docx', '.xlsx', '.pptx', '.html', '.htm',
                '.txt', '.md', '.csv', '.json', '.xml', '.epub',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
                '.mp3', '.wav', '.m4a', '.flac', '.ogg',
                '.zip', '.msg', '.ipynb'
            }

            added_count = 0
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if Path(file).suffix.lower() in supported_extensions:
                        file_path = os.path.join(root, file)
                        if file_path not in self.file_listbox.get(0, tk.END):
                            self.file_listbox.insert(tk.END, file_path)
                            added_count += 1

            messagebox.showinfo("完成", f"已添加 {added_count} 个文件")

    def clear_files(self):
        """清空文件列表"""
        self.file_listbox.delete(0, tk.END)

    def remove_selected_files(self):
        """删除选中的文件"""
        selection = self.file_listbox.curselection()
        for index in reversed(selection):
            self.file_listbox.delete(index)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)

    def open_output_dir(self):
        """打开输出目录"""
        output_path = self.output_dir.get()
        if os.path.exists(output_path):
            if os.name == 'nt':  # Windows
                os.startfile(output_path)
            else:  # macOS and Linux
                webbrowser.open(f'file://{os.path.abspath(output_path)}')
        else:
            messagebox.showwarning("警告", "输出目录不存在")

    def start_conversion(self):
        """开始转换"""
        if self.input_type.get() == "url":
            self.convert_urls()
        else:
            self.convert_files()

    def convert_urls(self):
        """转换 URL"""
        urls_text = self.url_text.get(1.0, tk.END).strip()
        if not urls_text:
            messagebox.showwarning("警告", "请输入要转换的 URL")
            return

        urls = [line.strip() for line in urls_text.split('\n') if line.strip()]
        if not urls:
            messagebox.showwarning("警告", "没有有效的 URL")
            return

        # 转换为字典格式
        urls_data = [{
            'account': 'unknown_account',
            'title': '',
            'url': url,
            'date': ''
        } for url in urls]

        self.run_conversion_thread(urls_data, is_url=True)

    def convert_files(self):
        """转换文件"""
        files = list(self.file_listbox.get(0, tk.END))
        if not files:
            messagebox.showwarning("警告", "请选择要转换的文件")
            return

        self.run_conversion_thread(files, is_url=False)

    def run_conversion_thread(self, items, is_url=True):
        """在后台线程中运行转换"""
        def conversion_worker():
            try:
                self.progress.start()
                self.status_text.set("转换中...")

                output_dir = self.output_dir.get()
                max_workers = self.max_workers.get()

                if is_url:
                    # URL 转换
                    os.makedirs(os.path.join(output_dir, "MD文档"), exist_ok=True)
                    success_count = 0

                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        futures = [executor.submit(self.converter.process_url, url_data, output_dir)
                                 for url_data in items]
                        for future in as_completed(futures):
                            if future.result():
                                success_count += 1

                    self.root.after(0, lambda: self.conversion_complete(len(items), success_count))
                else:
                    # 文件转换
                    os.makedirs(os.path.join(output_dir, "MD文档"), exist_ok=True)
                    success_count = 0

                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        futures = [executor.submit(self.converter.process_file, file_path,
                                                 os.path.join(output_dir, "MD文档"))
                                 for file_path in items]
                        for future in as_completed(futures):
                            if future.result():
                                success_count += 1

                    self.root.after(0, lambda: self.conversion_complete(len(items), success_count))

            except Exception as e:
                self.root.after(0, lambda: self.conversion_error(str(e)))

        # 启动后台线程
        thread = threading.Thread(target=conversion_worker, daemon=True)
        thread.start()

    def conversion_complete(self, total, success):
        """转换完成回调"""
        self.progress.stop()
        self.status_text.set(f"转换完成: 共 {total} 项，成功 {success} 项")
        messagebox.showinfo("完成", f"转换完成！\n共处理 {total} 项，成功 {success} 项")

    def conversion_error(self, error_msg):
        """转换错误回调"""
        self.progress.stop()
        self.status_text.set("转换失败")
        messagebox.showerror("错误", f"转换过程中发生错误: {error_msg}")

    def run(self):
        """运行 GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强型 Markdown 转换器')
    parser.add_argument('--gui', action='store_true', help='启动图形界面')
    parser.add_argument('-o', '--output', default='./articles', help='输出目录 (默认: ./articles)')
    parser.add_argument('--config', help='自定义配置文件路径')
    parser.add_argument('--workers', type=int, default=10, help='并发工作线程数 (默认: 10)')
    parser.add_argument('--file', help='要转换的单个文件路径')
    parser.add_argument('--url', help='要转换的单个 URL')

    args = parser.parse_args()

    if args.gui or (not args.file and not args.url):
        # 启动 GUI
        try:
            app = ConverterGUI()
            app.run()
        except Exception as e:
            print(f"GUI 启动失败: {str(e)}")
            print("请确保已安装 tkinter 库")
    else:
        # 命令行模式
        converter = EnhancedMarkdownConverter(args.config)

        if args.file:
            # 转换单个文件
            output_dir = os.path.join(args.output, "MD文档")
            success = converter.process_file(args.file, output_dir)
            if success:
                print(f"文件转换成功: {args.file}")
            else:
                print(f"文件转换失败: {args.file}")

        elif args.url:
            # 转换单个 URL
            url_data = {
                'account': 'unknown_account',
                'title': '',
                'url': args.url,
                'date': ''
            }
            success = converter.process_url(url_data, args.output)
            if success:
                print(f"URL 转换成功: {args.url}")
            else:
                print(f"URL 转换失败: {args.url}")

if __name__ == "__main__":
    main()
